import { Controller, Get, Post } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { CommonService } from './common.service';
import config from 'src/siteConfig';
import User from 'src/db/model/User';
import { InjectModel } from '@nestjs/sequelize';
import { pinyin } from 'pinyin-pro';

import * as chinaDivision from '../../utils/china-division.json'
import Party from 'src/db/model/Party';
import Venue from 'src/db/model/Venue';
import { Op } from 'sequelize';
import * as path from 'path';
import * as fs from 'fs';

// 读取名言文件
const quotesPath = path.join(process.cwd(), 'src/utils/quotes.json');
const quotes = JSON.parse(fs.readFileSync(quotesPath, 'utf8'));

function isChinese(str: string) {
  return /[\u4e00-\u9fa5]/.test(str);
}

@ApiTags('通用')
@Controller(`${config.API_PREFIX}/common`)
export class CommonController {
  constructor(
    private readonly commonService: CommonService,
      @InjectModel(User) 
      private userModel: typeof User,
      @InjectModel(Party)
      private partyModel: typeof Party,
      @InjectModel(Venue)
      private venueModel: typeof Venue,
  ) {}

  @Get('greeting')
  @ApiOperation({ summary: '获取随机问候语' })
  getGreeting() {
    const hour = new Date().getHours();
    let timeGreeting = '';
    
    if (hour >= 5 && hour < 12) {
      timeGreeting = '上午好！';
    } else if (hour >= 12 && hour < 14) {
      timeGreeting = '中午好！';
    } else if (hour >= 14 && hour < 18) {
      timeGreeting = '下午好！';
    } else {
      timeGreeting = '晚上好！';
    }

    // 从JSON文件中读取的名言列表
    const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
    return [timeGreeting,randomQuote];
  }
  
  @Get('getQnToken')
  @ApiOperation({ summary: '获取七牛上传token' })
  getQnToken() {
    const token = this.commonService.getQnToken();
    return token;
  }
  
  @Post('fixUserEnLocCity')
  @ApiOperation({ summary: '修复用户坐标城市' })
  async fixUserEnLocCity() {

    // 生成省份映射
    const provinceMap: Record<string, string> = {};
    const cityMap: Record<string, string> = {};
    
    const provinceSet = new Set<string>();
    const citySet = new Set<string>();
      

    for (const item of chinaDivision.data) {
      provinceSet.add(item.n);
      if (item.c) {
        for (const city of item.c) {
          citySet.add(city.n);
        }
      }
    }
    
    // 省份拼音大写映射
    provinceSet.forEach(province => {
      const py = pinyin(province, { toneType: 'none', type: 'array' })
        .join('');
      provinceMap[py] = province;
    });
    
    // 城市拼音大写映射
    citySet.forEach(city => {
      const py = pinyin(city, { toneType: 'none', type: 'array' })
        .join('');
      cityMap[py] = city;
    });

    // console.log(provinceMap,cityMap);

    const users = await this.userModel.findAll();
    for (const user of users) {
    // 1. 英文转中文
    let province = user.province?.toLowerCase();
    let city = user.city?.toLowerCase();
    let country = user.country;
    if(!province || !city || isChinese(province) || isChinese(city)) {
      continue;
    }
    if (province && provinceMap[province]) {
      province = provinceMap[province];
    }
    if (city && cityMap[city]) {
      city = cityMap[city];
    }
    if (country === 'China') {
      country = '中国';
    }

    // 2. 查找 divisionCode
    let divisionCode:string = '0';
    const cityItem = chinaDivision.data.find(
      x => x.n === province && x.c.find(y => y.n === city)?.v
    );
    if (cityItem) {
      divisionCode = cityItem.c.find(y => y.n === city)?.v || '0';
    }
    

    // 3. 更新
    await user.update({
      country,
      province,
      city,
      divisionCode
    });
    }
    return { success: true };
  }
  @Post('fixPartyLocCity')
  @ApiOperation({ summary: '修复约毽坐标城市' })
  async fixPartyLocCity() {

    const parties = await this.partyModel.findAll();
    for (const party of parties) {
      const {province, city} = party;
      const divisionCode = this.commonService.tryGetDivisionCode(province, city);
      //console.log(divisionCode);
      await party.update({divisionCode});

    }

    return true;
  }
  @Post('fixUserLocCity')
  @ApiOperation({ summary: '修复约毽坐标城市' })
  async fixUserLocCity() {

    const users = await this.userModel.findAll();
    for (const user of users) {
      const {province, city} = user;
      const divisionCode = this.commonService.tryGetDivisionCode(province||"", city||"");
      //console.log(divisionCode);
      await user.update({divisionCode});

    }

    return true;
  }

  @Post('fixVenueLocCity')
  @ApiOperation({ summary: '修复场馆坐标城市' })
  async fixVenueLocCity() {

    const venues = await this.venueModel.findAll({
      where:{
        divisionCode:{
          [Op.or]:[0,null]
        }
      }
    });
    for (const venue of venues) {
      //console.log(venue.toJSON());
      const {province, city} = venue;
      const divisionCode = this.commonService.tryGetDivisionCode(province||"", city||"");
      //console.log(divisionCode);
      await venue.update({divisionCode});
    }

    return true;
  }
} 