import { Module } from '@nestjs/common';
import { APP_GUARD, APP_INTERCEPTOR, APP_FILTER } from '@nestjs/core';
// import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthGuard } from './auth/guards/auth.guard';
import { ResponseInterceptor } from './utils/response.interceptor';
import { GlobalExceptionFilter } from './utils/exception.filter';

import { CommonModule } from './modules/common/common.module';
import { UserModule } from './modules/user/user.module';
import { VenueModule } from './modules/venue/venue.module';
import { WxModule } from './modules/wx/wx.module';
import { TeamModule } from './modules/team/team.module';
import { LeagueModule } from './modules/league/league.module';
import { MatchModule } from './modules/match/match.module';
import { DocumentModule } from './modules/document/document.module';
import { IndexModule } from './modules/index/index.module';
import { CommentModule } from './modules/comment/comment.module';
import { MyModule } from './modules/my/my.module';
import { PartyModule } from './modules/party/party.module';
import { CityTeamModule } from './modules/cityTeam/cityTeam.module';
import { NotificationModule } from './modules/notification/notification.module';
import { UserMessageModule } from './modules/userMessage/userMessage.module';

import siteConfig from './siteConfig';

@Module({
  imports: [
    // ConfigModule.forRoot({
    //   isGlobal: true,
    //   envFilePath: '.env',
    // }),
    JwtModule.registerAsync({
      // imports: [ConfigModule],
      useFactory: async () => ({
        secret: siteConfig.JWT_SECRET,
        signOptions: { expiresIn: '30d' },
      }),
    }),
    CityTeamModule,
    CommonModule,
    UserModule,
    VenueModule,
    WxModule,
    TeamModule,
    LeagueModule,
    MatchModule,
    DocumentModule,
    IndexModule,
    CommentModule,
    MyModule,
    PartyModule,
    NotificationModule,
    UserMessageModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    // {
    //   provide: APP_GUARD,
    //   useClass: RoleGuard,
    // },
    AppService,
  ],
})
export class AppModule {}
