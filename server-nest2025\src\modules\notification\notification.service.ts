import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import SystemNotification from '../../db/model/SystemNotification';
import User from '../../db/model/User';

import { CreateNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { QueryNotificationDto, AdminQueryNotificationDto } from './dto/query-notification.dto';
import { NotificationListResponseDto, NotificationStatsDto } from './dto/notification-response.dto';

@Injectable()
export class NotificationService {
  constructor(
    @InjectModel(SystemNotification)
    private readonly notificationModel: typeof SystemNotification,
    @InjectModel(User)
    private readonly userModel: typeof User,
  ) {}

  // 获取用户通知列表
  async getUserNotifications(query: QueryNotificationDto): Promise<NotificationListResponseDto> {
    const { page = 1, limit = 10, type, priority, keyword } = query;
    const offset = (page - 1) * limit;

    // 构建查询条件
    const whereConditions: any = {
      isActive: true,
      publishAt: { [Op.lte]: new Date() },
      [Op.or]: [
        { expireAt: null },
        { expireAt: { [Op.gt]: new Date() } }
      ]
    };

    if (type) {
      whereConditions.type = type;
    }

    if (priority) {
      whereConditions.priority = priority;
    }

    if (keyword) {
      whereConditions[Op.or] = [
        { title: { [Op.like]: `%${keyword}%` } },
        { content: { [Op.like]: `%${keyword}%` } }
      ];
    }

    // 获取所有符合条件的通知
    const { rows: notifications, count: total } = await this.notificationModel.findAndCountAll({
      where: whereConditions,
      order: [['publishAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(total / limit);

    return {
      list: notifications.map(n => n.toJSON()),
      total,
      page,
      limit,
      totalPages
    };
  }

  // 管理员获取通知列表
  async getAdminNotifications(query: AdminQueryNotificationDto): Promise<NotificationListResponseDto> {
    const { page = 1, limit = 10, type, priority, keyword, isActive, createdBy } = query;
    const offset = (page - 1) * limit;

    const whereConditions: any = {};

    if (type) {
      whereConditions.type = type;
    }

    if (priority) {
      whereConditions.priority = priority;
    }

    if (typeof isActive === 'boolean') {
      whereConditions.isActive = isActive;
    }

    if (createdBy) {
      whereConditions.createdBy = createdBy;
    }

    if (keyword) {
      whereConditions[Op.or] = [
        { title: { [Op.like]: `%${keyword}%` } },
        { content: { [Op.like]: `%${keyword}%` } }
      ];
    }

    const { rows: notifications, count: total } = await this.notificationModel.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'name', 'nickName']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(total / limit);

    return {
      list: notifications.map(n => n.toJSON()),
      total,
      page,
      limit,
      totalPages
    };
  }

  // 创建通知
  async createNotification(createDto: CreateNotificationDto, createdBy: string): Promise<SystemNotification> {
    const notificationData = {
      ...createDto,
      createdBy,
      publishAt: createDto.publishAt || new Date(),
      priority: createDto.priority || 'info',
      isActive: createDto.isActive !== false
    };

    return await this.notificationModel.create(notificationData);
  }

  // 更新通知
  async updateNotification(id: string, updateDto: UpdateNotificationDto): Promise<SystemNotification> {
    const notification = await this.notificationModel.findByPk(id);
    if (!notification) {
      throw new NotFoundException('通知不存在');
    }

    await notification.update(updateDto);
    return notification;
  }

  // 删除通知
  async deleteNotification(id: string): Promise<void> {
    const notification = await this.notificationModel.findByPk(id);
    if (!notification) {
      throw new NotFoundException('通知不存在');
    }

    await notification.destroy();
  }

  // 获取通知详情
  async getNotificationById(id: string): Promise<SystemNotification> {
    const notification = await this.notificationModel.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'name', 'nickName']
        }
      ]
    });

    if (!notification) {
      throw new NotFoundException('通知不存在');
    }

    return notification;
  }

  // 获取通知统计
  async getNotificationStats(): Promise<NotificationStatsDto> {
    const total = await this.notificationModel.count();
    const active = await this.notificationModel.count({ where: { isActive: true } });
    const expired = await this.notificationModel.count({
      where: {
        expireAt: { [Op.lt]: new Date() }
      }
    });
    const system = await this.notificationModel.count({ where: { type: 'system' } });
    const announcement = await this.notificationModel.count({ where: { type: 'announcement' } });

    return { total, active, expired, system, announcement };
  }


}
