import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query 
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { UserMessageService } from './userMessage.service';
import { CreateMessageDto } from './dto/create-message.dto';
import { QueryMessageDto, ConversationQueryDto, UnreadCountDto } from './dto/query-message.dto';
import { 
  MessageResponseDto, 
  MessageListResponseDto, 
  ConversationListDto, 
  UnreadCountResponseDto 
} from './dto/message-response.dto';
import { RequestUser } from '../../auth/decorators/requestUser.decorator';
import config from '../../siteConfig';

@ApiTags('用户消息')
@Controller(`${config.API_PREFIX}/user-message`)
@ApiBearerAuth('JWT-auth')
export class UserMessageController {
  constructor(private readonly userMessageService: UserMessageService) {}

  @Post('send')
  @ApiOperation({ summary: '发送消息' })
  @ApiResponse({ status: 201, description: '成功发送消息', type: MessageResponseDto })
  async sendMessage(
    @Body() createDto: CreateMessageDto,
    @RequestUser() user: any
  ): Promise<MessageResponseDto> {
    return await this.userMessageService.sendMessage(user.id, createDto);
  }

  @Get('conversations')
  @ApiOperation({ summary: '获取对话列表' })
  @ApiResponse({ status: 200, description: '成功获取对话列表', type: ConversationListDto })
  async getConversations(
    @Query() query: QueryMessageDto,
    @RequestUser() user: any
  ): Promise<ConversationListDto> {
    return await this.userMessageService.getConversations(user.id, query);
  }

  @Get('conversation/:userId')
  @ApiOperation({ summary: '获取与特定用户的对话消息' })
  @ApiResponse({ status: 200, description: '成功获取对话消息', type: MessageListResponseDto })
  async getConversationMessages(
    @Param('userId') otherUserId: string,
    @Query() query: ConversationQueryDto,
    @RequestUser() user: any
  ): Promise<MessageListResponseDto> {
    return await this.userMessageService.getConversationMessages(user.id, otherUserId, query);
  }

  @Put(':id/read')
  @ApiOperation({ summary: '标记消息为已读' })
  @ApiResponse({ status: 200, description: '成功标记消息为已读' })
  async markAsRead(
    @Param('id') messageId: string,
    @RequestUser() user: any
  ): Promise<{ message: string }> {
    await this.userMessageService.markAsRead(messageId, user.id);
    return { message: '消息已标记为已读' };
  }

  @Put('conversation/:userId/read')
  @ApiOperation({ summary: '标记对话中所有消息为已读' })
  @ApiResponse({ status: 200, description: '成功标记对话为已读' })
  async markConversationAsRead(
    @Param('userId') otherUserId: string,
    @RequestUser() user: any
  ): Promise<{ message: string }> {
    await this.userMessageService.markConversationAsRead(user.id, otherUserId);
    return { message: '对话已标记为已读' };
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除消息' })
  @ApiResponse({ status: 200, description: '成功删除消息' })
  async deleteMessage(
    @Param('id') messageId: string,
    @RequestUser() user: any
  ): Promise<{ message: string }> {
    await this.userMessageService.deleteMessage(messageId, user.id);
    return { message: '消息删除成功' };
  }

  @Get('unread-count')
  @ApiOperation({ summary: '获取未读消息数量' })
  @ApiResponse({ status: 200, description: '成功获取未读消息数量', type: UnreadCountResponseDto })
  async getUnreadCount(
    @Query() query: UnreadCountDto,
    @RequestUser() user: any
  ): Promise<UnreadCountResponseDto> {
    return await this.userMessageService.getUnreadCount(user.id, query);
  }
}
