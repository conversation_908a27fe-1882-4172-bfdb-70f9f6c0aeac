import { SequelizeModule } from '@nestjs/sequelize';
import { Module } from '@nestjs/common';
import siteConfig from '../siteConfig';

import Account from './model/Account';
import Asset from './model/Asset';
import Document from './model/Document';
import League from './model/League';
import LeagueTeam from './model/LeagueTeam';
import User from './model/User';
import UserComment from './model/UserComment';
import UserGallery from './model/UserGallery';
import UserManagerApply from './model/UserManagerApply';
import Venue from './model/Venue';
import VenueHistory from './model/VenueHistory';
import VenueStatic from './model/VenueStatic';
import Match from './model/Match';
import MatchClass from './model/MatchClass';
import MatchTeamPlayer from './model/MatchTeamPlayer';
import Team from './model/Team';
import MatchTeam from './model/MatchTeam';
import Player from './model/Player';
import TeamPlayer from './model/TeamPlayer';
import VenueVisitLog from './model/VenueVisitLog';
import Party from './model/Party';
import PartyInterest from './model/PartyInterest';
import PartyStatic from './model/PartyStatic';
import UserPost from './model/UserPost';
import UserLikeLog from './model/UserLikeLog';
import LeagueAccount from './model/LeagueAccount';
import LeagueTeamResult from './model/LeagueTeamResult';
import MatchTeamResult from './model/MatchTeamResult';
import Sport from './model/Sport';
import LeagueMatchClass from './model/LeagueMatchClass';
import CityTeam from './model/CityTeam';
import SystemNotification from './model/SystemNotification';
import UserMessage from './model/UserMessage';

//console.log(siteConfig.DB_HOST, siteConfig.DB_PORT, siteConfig.DB_USER, siteConfig.DB_PWD, siteConfig.DB_NAME);
@Module({
  imports: [
    SequelizeModule.forRoot({
      dialect: 'mysql',
      host: siteConfig.DB_HOST,
      port: parseInt(siteConfig.DB_PORT || '1433'),
      username: siteConfig.DB_USER,
      password: siteConfig.DB_PWD,
      database: siteConfig.DB_NAME,
      synchronize: false,
      autoLoadModels: true,
      // define: {
      //   charset: 'utf8mb4',
      //   freezeTableName: true,//不使用复数表名
      // },
      dialectOptions: {
        charset:'utf8mb4_general_ci',
        dateStrings: true, //date 输出格式
        typeCast: true
      },
      pool: {
        max: 10,
        min: 0,
        idle: 10000
      },
      models: [
        Sport,
        Account, Document, Asset, League, LeagueTeam, Match, MatchClass,
        MatchTeam, Player, Team, TeamPlayer, MatchTeamPlayer, Venue,
        VenueHistory, VenueStatic, VenueVisitLog, Party, PartyInterest,
        PartyStatic, User, UserComment, UserGallery, UserPost,
        UserLikeLog, UserManagerApply, LeagueAccount, LeagueTeamResult, MatchTeamResult, LeagueMatchClass,
        CityTeam, SystemNotification, UserMessage,
      ],
    }),
  ],
  exports: [SequelizeModule],
})
export class db {}