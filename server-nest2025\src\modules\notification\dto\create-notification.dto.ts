import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsDateString, IsBoolean, IsObject } from 'class-validator';

export class CreateNotificationDto {
  @ApiProperty({ description: '通知标题' })
  @IsString()
  title: string;

  @ApiProperty({ description: '通知内容' })
  @IsString()
  content: string;

  @ApiProperty({ 
    description: '通知类型', 
    enum: ['broadcast', 'targeted'],
    example: 'broadcast'
  })
  @IsEnum(['broadcast', 'targeted'])
  type: 'broadcast' | 'targeted';

  @ApiPropertyOptional({ 
    description: '目标用户条件',
    example: {
      provinces: ['北京市', '上海市'],
      cities: ['朝阳区', '浦东新区'],
      minLevel: 1,
      maxLevel: 10
    }
  })
  @IsOptional()
  @IsObject()
  targetConditions?: {
    provinces?: string[];
    cities?: string[];
    minLevel?: number;
    maxLevel?: number;
    userTypes?: string[];
    divisionCodes?: string[];
  };

  @ApiPropertyOptional({ 
    description: '通知优先级', 
    enum: ['info', 'warning', 'success', 'error'],
    default: 'info'
  })
  @IsOptional()
  @IsEnum(['info', 'warning', 'success', 'error'])
  priority?: 'info' | 'warning' | 'success' | 'error';

  @ApiPropertyOptional({ 
    description: '操作数据',
    example: {
      type: 'url',
      value: 'https://example.com',
      buttonText: '查看详情'
    }
  })
  @IsOptional()
  @IsObject()
  actionData?: {
    type?: 'url' | 'route' | 'modal';
    value?: string;
    buttonText?: string;
  };

  @ApiPropertyOptional({ description: '发布时间' })
  @IsOptional()
  @IsDateString()
  publishAt?: Date;

  @ApiPropertyOptional({ description: '过期时间' })
  @IsOptional()
  @IsDateString()
  expireAt?: Date;

  @ApiPropertyOptional({ description: '是否激活', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
