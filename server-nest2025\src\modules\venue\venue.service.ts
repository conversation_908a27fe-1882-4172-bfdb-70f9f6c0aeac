import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { IndexService } from '../index/index.service';
import { queryPaginDataAsync, parseRequestToModel, pickDefinedFields, getCityDivisionRange } from '../../utils/funs';
import { AccountType, UserLevel, LikeType } from '../../utils/enums';
import { CreateOrUpdateVenueDto } from './dto/create-update-venue.dto';
import { VisitVenueDto } from './dto/visit-venue.dto';
import { PostAlbumDto } from './dto/post-album.dto';
import Venue from '../../db/model/Venue';
import VenueStatic from '../../db/model/VenueStatic';
import User from '../../db/model/User';
import UserLikeLog from '../../db/model/UserLikeLog';
import VenueVisitLog from '../../db/model/VenueVisitLog';
import VenueHistory from '../../db/model/VenueHistory';
import UserPost from '../../db/model/UserPost';
import UserGallery from '../../db/model/UserGallery';
import { AuthService } from '../auth/auth.service';
import { Op, Sequelize } from 'sequelize';

@Injectable()
export class VenueService {
  constructor(
    @InjectModel(Venue)
    private venueModel: typeof Venue,
    @InjectModel(VenueStatic)
    private venueStaticModel: typeof VenueStatic,
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(UserLikeLog)
    private userLikeLogModel: typeof UserLikeLog,
    @InjectModel(VenueVisitLog)
    private venueVisitLogModel: typeof VenueVisitLog,
    @InjectModel(VenueHistory)
    private venueHistoryModel: typeof VenueHistory,
    @InjectModel(UserPost)
    private userPostModel: typeof UserPost,
    @InjectModel(UserGallery)
    private userGalleryModel: typeof UserGallery,
    private indexService: IndexService,
    private authService: AuthService,
  ) {}

  async getVenues(query: {divisionCode?: number,province?: string,city?: string,pageIndex?: number,pageSize?: number}) {
    let where: any = {
      disabled: 0,
    };
    if(query.divisionCode){
      const {max,min} = getCityDivisionRange(query.divisionCode);
      where.divisionCode = {
        [Op.between]: [min,max]
      };
    }
    if(query.province){
      where.province = query.province;
    }
    if(query.city){
      where.city = query.city;
    }
    const venues = await queryPaginDataAsync(
      this.venueModel,
      query,
      {
        where,
        order: [['rank', 'DESC'],['updatedAt', 'DESC']],
        include: [{
          model: this.venueStaticModel
        },
        {
          association: this.venueModel.belongsTo(this.userModel, {
            foreignKey: 'createUserId',
          }),
          attributes: ['id', 'nickName', 'avatar'],
        }]
      },
      true
    );

    return venues;
  }

  async getTop(query: {divisionCode?: number,top?: number, isRandom?:boolean}) {
    let where: any = {
      disabled: 0,
    };
    if(query.divisionCode){
      const {max,min} = getCityDivisionRange(query.divisionCode);
      where.divisionCode = {
        [Op.between]: [min,max]
      };
    }

    let data = await this.venueModel.findAll({
      where,
      limit: Number(query.top || 5),
      order: query.isRandom ? [Sequelize.literal('RAND()')]:[['rank', 'DESC'],['createdAt', 'DESC']],
      include: [{
        model: this.venueStaticModel
      },
      {
        association: this.venueModel.belongsTo(this.userModel, {
          foreignKey: 'createUserId',
        }),
        attributes: ['id', 'nickName', 'avatar'],
      }],
      raw: true,
      nest: true,
    });
    return data;
  }

  // async getVenuesForSelect(where: string) {
  //   const { province, city } = JSON.parse(where);
  //   if (!province || !city) {
  //     throw new BadRequestException('缺少参数');
  //   }

  //   return await this.venueModel.findAll({
  //     where: {
  //       province,
  //       city,
  //       disabled: 0,
  //     }
  //   });
  // }

  async getVenue(id: string, user?: any) {
    const data = await this.venueModel.findByPk(id, {
      include: [{
        model: this.venueStaticModel
      },
      {
        association: this.venueModel.belongsTo(this.userModel, {
          foreignKey: 'createUserId',
        }),
        attributes: ['id', 'nickName', 'avatar'],
      }]
    });

    if (!data) {
      throw new BadRequestException('场馆不存在');
    }

    data.Static.hotCount++;
    await data.Static.save();

    const venueData = data.toJSON();
    venueData.isLike = false;

    if (user?.id) {
      const count = await this.userLikeLogModel.count({
        where: {
          targetId: id,
          type: LikeType.Venue,
          UserId: user.id
        }
      });
      if (count !== 0) {
        venueData.isLike = true;
      }
    }

    return venueData;
  }

  async deleteVenue(id: string, user: ITokenUser) {
    //await this.authService.checkRole(user.id, { types: [AccountType.Operator, AccountType.Admin] });
    const venue = await this.venueModel.findByPk(id);
    if (!venue) {
      throw new BadRequestException('场馆不存在');
    }

    if (venue.createUserId !== user.id) {
      // 检查用户权限

      if (!user || ![AccountType.Operator, AccountType.Admin].includes(user.type)) {
        throw new UnauthorizedException('没有权限');
      }
    }

    venue.disabled = 1;
    await venue.save();
    return true;
  }

  async createOrUpdateVenue(dto: CreateOrUpdateVenueDto, user: ITokenUser) {
    const cacheData = this.indexService.getCacheData();
    if (cacheData.sys['allowPushVenueByUser'] === '1') {
      // 检查用户等级
      if (!user || ![UserLevel.Member, UserLevel.Vip].includes(user.level)) {
        throw new UnauthorizedException('没有权限');
      }
    } else {
      // 检查用户类型
      if (!user || ![AccountType.Operator, AccountType.Admin].includes(user.type)) {
        throw new UnauthorizedException('没有权限');
      }
    }

     dto.province = dto.province.replace(/省$/, '').replace(/市$/, '');
     dto.city = dto.city.replace(/市$/, '');

     if (dto.id) {
       // 更新
       const existingVenue = await this.venueModel.findOne({
         where: { id: dto.id }
       });

       if (!existingVenue) {
         throw new BadRequestException('场馆不存在');
       }

       const compareResult = this.objectCompare(existingVenue, dto);
       if (compareResult.length === 0) {
         throw new BadRequestException('未有变更');
       }
       Object.assign(existingVenue, dto);
       existingVenue.updateUserId = user.id;
       await existingVenue.save();

       // 记录历史
       await this.venueHistoryModel.create({
         VenueId: dto.id,
         UserId: user.id,
         content: JSON.stringify(compareResult),
       });

       return existingVenue;
     } else {
       // 新建
       // 删除id字段让数据库自动生成UUID，确保空字符串也被删除
       if (!dto.id) {
         delete (dto as any).id;
       }


       try {
         const data = await this.venueModel.create<Venue>({
          ...dto,
          createUserId: user.id,
          updateUserId: user.id,
          rank: dto.rank || new Date().getTime(),
          Static: {
            visitCount: 0,
            likeCount: 0
          }
        }, {
           include: [this.venueStaticModel],
           returning: true,
         });

         return {id:data.id};
       } catch (e) {
         console.log('创建场馆失败:', e);
         if (e.code === 'ER_DUP_ENTRY') {
           throw new BadRequestException('场馆已存在或ID重复');
         }
         throw new BadRequestException('创建场馆失败: ' + e.message);
       }
     }
  }

  async removeVenue(id: string) {
    const venue = await this.venueModel.findByPk(id);
    if (!venue) {
      throw new BadRequestException('场馆不存在');
    }

    await venue.destroy();
    return true;
  }

  async likeVenue(id: string, user: any) {
    const userData = await this.userModel.findByPk(user.id);
    if (!userData || ![UserLevel.Member, UserLevel.Vip].includes(userData.level)) {
      throw new UnauthorizedException('没有权限');
    }

    const log = await this.userLikeLogModel.findOne({
      where: {
        UserId: user.id,
        targetId: id
      }
    });

    const venueStatic = await this.venueStaticModel.findByPk(id);
    if (!venueStatic) {
      throw new BadRequestException('场馆不存在');
    }

    if (log) {
      venueStatic.likeCount -= 1;
      await log.destroy();
    } else {
      venueStatic.likeCount += 1;
      await this.userLikeLogModel.create({
        UserId: user.id,
        targetId: id,
        type: LikeType.Venue,
      });
    }

    await venueStatic.save();
    return {
      result: !log,
      count: venueStatic.likeCount
    };
  }

  async visitVenue(id: string, visitVenueDto: VisitVenueDto, user: any) {
    const userData = await this.userModel.findByPk(user.id);
    if (!userData || ![UserLevel.Member, UserLevel.Vip].includes(userData.level)) {
      throw new UnauthorizedException('没有权限');
    }

    const log = await this.venueVisitLogModel.findOne({
      where: {
        UserId: user.id,
        VenueId: id
      },
      order: [['updatedAt', 'DESC']]
    });

    if (log && (log.updatedAt.setHours(0, 0, 0, 0) === new Date().setHours(0, 0, 0, 0))) {
      return 0;
    }

    const venue = await this.venueModel.findByPk(id);
    if (!venue) {
      throw new BadRequestException('场馆不存在');
    }

    const venueStatic = await this.venueStaticModel.findByPk(id);
    if (!venueStatic) {
      throw new BadRequestException('场馆不存在');
    }

    venueStatic.visitCount++;
    await venueStatic.save();

    await this.venueVisitLogModel.create({
      UserId: user.id,
      VenueId: id,
      name: venue.name,
      lat: visitVenueDto.lat,
      lon: visitVenueDto.lon
    });

    return 1;
  }

  async getVenueVisits(id: string, query: any) {
    return await queryPaginDataAsync(
      this.venueVisitLogModel,
      query,
      {
        where: { VenueId: id },
        order: [['updatedAt', 'DESC']],
        include: [{
          model: this.userModel,
          attributes: ['nickName', 'id', 'avatar']
        }]
      }
    );
  }

  async getVenueUpdateHistory(id: string, query: any) {
    return await queryPaginDataAsync(
      this.venueHistoryModel,
      query,
      {
        where: { VenueId: id },
        order: [['updatedAt', 'DESC']],
        include: [{
          association: this.venueHistoryModel.belongsTo(this.userModel, {
            foreignKey: 'UserId',
            targetKey: 'id'
          }),
          attributes: ['id', 'nickName', 'avatar'],
        }]
      }
    );
  }
  //场地图库发布
  async postAlbum(postAlbumDto: PostAlbumDto, user: ITokenUser) {
    const cacheData = this.indexService.getCacheData();
    if (cacheData.sys['allowComment'] == 1) {
      if (!user || ![UserLevel.Member, UserLevel.Vip].includes(user.level)) {
        throw new UnauthorizedException('没有权限');
      }
    } else {
      if (!user || ![AccountType.Operator, AccountType.Admin].includes(user.type)) {
        throw new UnauthorizedException('没有权限');
      }
    }

    if (postAlbumDto.photos.length === 0) {
      throw new BadRequestException('请上传图片');
    }

    let postAlbum = new UserPost({...postAlbumDto,UserId:user.id});

    const venue = await this.venueModel.findByPk(postAlbumDto.venueId);
    
    if (!venue) {
      throw new BadRequestException('场馆不存在');
    }

    const _postAlbum = await this.userPostModel.create<UserPost>(postAlbum.toJSON());

    await (venue as any).addPost(_postAlbum);
    return _postAlbum;
  }

  private objectCompare(source: any, target: any) {
    if (!target) {
      throw new Error('target object is null');
    }
    if (!source) {
      throw new Error('source object is null');
    }

    const result:any[] = [];
    const targetKeys = Object.keys(target);

    targetKeys.forEach(key => {
      if (key in source) {
        let v = false;
        let old = source[key];
        let _new = target[key];

        if (old instanceof Date) {
          try {
            old = new Date(old).toISOString();
            _new = new Date(_new).toISOString();
            v = old !== _new;
          } catch {
            v = true;
          }
        } else {
          v = old !== _new;
        }

        if (v) {
          result.push({
            field: key,
            old,
            new: _new
          });
        }
      }
    });

    return result;
  }
} 