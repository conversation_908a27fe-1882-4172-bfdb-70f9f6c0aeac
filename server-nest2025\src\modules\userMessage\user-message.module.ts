import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { UserMessageController } from './user-message.controller';
import { UserMessageService } from './user-message.service';
import UserMessage from '../../db/model/UserMessage';
import User from '../../db/model/User';

@Module({
  imports: [
    SequelizeModule.forFeature([UserMessage, User])
  ],
  controllers: [UserMessageController],
  providers: [UserMessageService],
  exports: [UserMessageService]
})
export class UserMessageModule {}
