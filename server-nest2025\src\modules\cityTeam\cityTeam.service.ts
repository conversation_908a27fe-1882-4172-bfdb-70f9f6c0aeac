import { Injectable, BadRequestException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, Sequelize } from 'sequelize';
import CityTeam from '../../db/model/CityTeam';
import User from '../../db/model/User';
import Sport from '../../db/model/Sport';
import { CityTeamDto } from './dto/cityTeam.dto';
import { QueryCityTeamDto } from './dto/query-cityTeam.dto';
import { AccountType } from '../../utils/enums';
import { getCityDivisionRange } from 'src/utils/funs';

@Injectable()
export class CityTeamService {
  constructor(
    @InjectModel(CityTeam)
    private cityTeamModel: typeof CityTeam,
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(Sport)
    private sportModel: typeof Sport,
  ) {}

  async getCityTeams(query: QueryCityTeamDto) {
    const { page = 1, limit = 10, keyword, SportId, type, province, city } = query;
    const offset = (page - 1) * limit;

    const where: any = {
      disabled: { [Op.ne]: 1 }
    };

    if (keyword) {
      where[Op.or] = [
        { name: { [Op.like]: `%${keyword}%` } },
        { intro: { [Op.like]: `%${keyword}%` } },
        { leader: { [Op.like]: `%${keyword}%` } }
      ];
    }

    if (SportId) {
      where.SportId = SportId;
    }

    if (type !== undefined) {
      where.type = type;
    }

    if (province) {
      where.province = province;
    }

    if (city) {
      where.city = city;
    }

    const result = await this.cityTeamModel.findAndCountAll({
      where,
      order: [
        ['rank', 'DESC'],
        ['createdAt', 'DESC']
      ],
      include: [
        {
          model: this.userModel,
          as: 'CreatedUser',
          attributes: ['nickName', 'id', 'avatar']
        }
      ],
      limit,
      offset,
      raw: true,
      nest: true
    });

    return {
      rows: result.rows,
      count: result.count,
      page,
      limit,
      totalPages: Math.ceil(result.count / limit)
    };
  }

  async getTop(query: {divisionCode?: number,top?: number,isRandom?:boolean}) {
    const {divisionCode,top,isRandom} = query;
    const where: any = {
      disabled: 0,
    };
    if(divisionCode){
      const {max,min} = getCityDivisionRange(divisionCode);
      where.divisionCode = {
        [Op.between]: [min,max]
      };
    }
    const data = await this.cityTeamModel.findAll({
      where,
      order: isRandom ? [Sequelize.literal('RAND()')]:[['rank', 'DESC'],['createdAt', 'DESC']],
      limit: Number(top || 5),
      include: [
        {
          model: this.userModel,
          as: 'CreatedUser',
          attributes: ['nickName', 'id', 'avatar']
        }
      ],
      raw: true,
      nest: true
    });
    return data;
  }

  async getCityTeamById(id: number) {
    const cityTeam = await this.cityTeamModel.findByPk(id, {
      include: [
        {
          model: this.sportModel,
          attributes: ['name', 'id']
        },
        {
          model: this.userModel,
          as: 'CreatedUser',
          attributes: ['nickName', 'id', 'avatar']
        }
      ],
      raw: true,
      nest: true
    });

    if (!cityTeam || cityTeam.disabled === 1) {
      throw new NotFoundException('城市队不存在');
    }

    return cityTeam;
  }



  async createOrUpdateCityTeam(updateCityTeamDto: CityTeamDto, user: any) {
    let cityTeam:CityTeam|null;
    if(!updateCityTeamDto.id){
      cityTeam = await this.cityTeamModel.create({
        ...updateCityTeamDto,
        CreatedUserId: user.id,
        rank: 0
      });
    }else{
      cityTeam = await this.cityTeamModel.findByPk(updateCityTeamDto.id);
    
      if (!cityTeam) {
        throw new NotFoundException('城市队不存在');
      }
  
      if (cityTeam.disabled === 1) {
        throw new BadRequestException('城市队已被删除');
      }
  
      // 检查权限：只有创建者或管理员可以修改
      if (cityTeam.CreatedUserId !== user.id && user.type !== AccountType.Admin) {
        throw new ForbiddenException('没有权限修改此城市队');
      }
  
      cityTeam = await cityTeam.update(updateCityTeamDto);

    }


    return cityTeam;
  }

  async deleteCityTeam(id: string, user: any) {
    const cityTeam = await this.cityTeamModel.findByPk(id);
    
    if (!cityTeam) {
      throw new NotFoundException('城市队不存在');
    }

    if (cityTeam.disabled === 1) {
      throw new BadRequestException('城市队已被删除');
    }

    // 检查权限：只有创建者或管理员可以删除
    if (cityTeam.CreatedUserId !== user.id && user.type !== AccountType.Admin) {
      throw new ForbiddenException('没有权限删除此城市队');
    }

    cityTeam.disabled = 1;
    await cityTeam.save();
    
    return { message: '删除成功' };
  }

  async setRank(id: string, rank: number, user: any) {
    const cityTeam = await this.cityTeamModel.findByPk(id);
    
    if (!cityTeam) {
      throw new NotFoundException('城市队不存在');
    }

    if (cityTeam.disabled === 1) {
      throw new BadRequestException('城市队已被删除');
    }

    // 检查权限：只有创建者或管理员可以设置排序
    if (cityTeam.CreatedUserId !== user.id && user.type !== AccountType.Admin) {
      throw new ForbiddenException('没有权限设置此城市队的排序');
    }

    cityTeam.rank = rank;
    await cityTeam.save();
    
    return cityTeam;
  }

  async getCityTeamsBySport(sportId: string, limit: number = 10) {
    return await this.cityTeamModel.findAll({
      where: {
        SportId: sportId,
        disabled: { [Op.ne]: 1 }
      },
      order: [
        ['rank', 'DESC'],
        ['createdAt', 'DESC']
      ],
      include: [
        {
          model: this.sportModel,
          attributes: ['name', 'id']
        }
      ],
      limit
    });
  }
} 