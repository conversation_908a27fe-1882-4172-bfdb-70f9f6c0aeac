import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, fn, col, literal } from 'sequelize';
import UserMessage from '../../db/model/UserMessage';
import User from '../../db/model/User';
import { CreateMessageDto } from './dto/create-message.dto';
import { QueryMessageDto, ConversationQueryDto, UnreadCountDto } from './dto/query-message.dto';
import { 
  MessageListResponseDto, 
  ConversationListDto, 
  UnreadCountResponseDto,
  MessageResponseDto,
  ConversationDto
} from './dto/message-response.dto';

@Injectable()
export class UserMessageService {
  constructor(
    @InjectModel(UserMessage)
    private readonly messageModel: typeof UserMessage,
    @InjectModel(User)
    private readonly userModel: typeof User,
  ) {}

  // 发送消息
  async sendMessage(senderId: string, createDto: CreateMessageDto): Promise<MessageResponseDto> {
    // 验证接收者是否存在
    const receiver = await this.userModel.findByPk(createDto.receiverId);
    if (!receiver) {
      throw new NotFoundException('接收者不存在');
    }

    // 如果是回复消息，验证原消息是否存在
    if (createDto.replyToId) {
      const originalMessage = await this.messageModel.findByPk(createDto.replyToId);
      if (!originalMessage) {
        throw new NotFoundException('回复的消息不存在');
      }
    }

    const messageData = {
      senderId,
      receiverId: createDto.receiverId,
      content: createDto.content,
      messageType: createDto.messageType || 'text',
      attachments: createDto.attachments,
      replyToId: createDto.replyToId
    };

    const message = await this.messageModel.create(messageData);
    
    // 返回完整的消息信息
    return await this.getMessageWithDetails(message.id);
  }

  // 获取对话列表
  async getConversations(userId: string, query: QueryMessageDto): Promise<ConversationListDto> {
    const { page = 1, limit = 20, keyword } = query;
    const offset = (page - 1) * limit;

    // 获取用户参与的所有对话的最新消息
    const subQuery = `
      SELECT 
        CASE 
          WHEN senderId = '${userId}' THEN receiverId 
          ELSE senderId 
        END as otherUserId,
        MAX(createdAt) as lastMessageTime
      FROM UserMessages 
      WHERE (senderId = '${userId}' OR receiverId = '${userId}') 
        AND isDeleted = false
      GROUP BY otherUserId
      ORDER BY lastMessageTime DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    const conversationUsers = await this.messageModel.sequelize.query(subQuery, {
      type: 'SELECT'
    }) as any[];

    const conversations: ConversationDto[] = [];

    for (const conv of conversationUsers) {
      const otherUser = await this.userModel.findByPk(conv.otherUserId, {
        attributes: ['id', 'name', 'nickName', 'avatar']
      });

      if (!otherUser) continue;

      // 获取最后一条消息
      const lastMessage = await this.messageModel.findOne({
        where: {
          [Op.or]: [
            { senderId: userId, receiverId: conv.otherUserId },
            { senderId: conv.otherUserId, receiverId: userId }
          ],
          isDeleted: false
        },
        order: [['createdAt', 'DESC']]
      });

      // 获取未读消息数
      const unreadCount = await this.messageModel.count({
        where: {
          senderId: conv.otherUserId,
          receiverId: userId,
          isRead: false,
          isDeleted: false
        }
      });

      if (lastMessage) {
        conversations.push({
          userId: conv.otherUserId,
          userInfo: otherUser.toJSON(),
          lastMessage: {
            id: lastMessage.id,
            content: lastMessage.content,
            messageType: lastMessage.messageType,
            createdAt: lastMessage.createdAt,
            isRead: lastMessage.isRead,
            senderId: lastMessage.senderId
          },
          unreadCount,
          lastUpdateTime: lastMessage.createdAt
        });
      }
    }

    return {
      list: conversations,
      total: conversations.length
    };
  }

  // 获取与特定用户的对话消息
  async getConversationMessages(
    userId: string, 
    otherUserId: string, 
    query: ConversationQueryDto
  ): Promise<MessageListResponseDto> {
    const { page = 1, limit = 20 } = query;
    const offset = (page - 1) * limit;

    const { rows: messages, count: total } = await this.messageModel.findAndCountAll({
      where: {
        [Op.or]: [
          { senderId: userId, receiverId: otherUserId },
          { senderId: otherUserId, receiverId: userId }
        ],
        isDeleted: false
      },
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'name', 'nickName', 'avatar']
        },
        {
          model: User,
          as: 'receiver',
          attributes: ['id', 'name', 'nickName', 'avatar']
        },
        {
          model: UserMessage,
          as: 'replyTo',
          attributes: ['id', 'content', 'senderId'],
          include: [
            {
              model: User,
              as: 'sender',
              attributes: ['name', 'nickName']
            }
          ],
          required: false
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(total / limit);

    return {
      list: messages.map(message => this.formatMessageResponse(message)),
      total,
      page,
      limit,
      totalPages
    };
  }

  // 标记消息为已读
  async markAsRead(messageId: string, userId: string): Promise<void> {
    const message = await this.messageModel.findByPk(messageId);
    
    if (!message) {
      throw new NotFoundException('消息不存在');
    }

    // 只有接收者可以标记消息为已读
    if (message.receiverId !== userId) {
      throw new ForbiddenException('无权限操作此消息');
    }

    if (!message.isRead) {
      await message.update({
        isRead: true,
        readAt: new Date()
      });
    }
  }

  // 批量标记对话消息为已读
  async markConversationAsRead(userId: string, otherUserId: string): Promise<void> {
    await this.messageModel.update(
      {
        isRead: true,
        readAt: new Date()
      },
      {
        where: {
          senderId: otherUserId,
          receiverId: userId,
          isRead: false,
          isDeleted: false
        }
      }
    );
  }

  // 删除消息
  async deleteMessage(messageId: string, userId: string): Promise<void> {
    const message = await this.messageModel.findByPk(messageId);
    
    if (!message) {
      throw new NotFoundException('消息不存在');
    }

    // 只有发送者或接收者可以删除消息
    if (message.senderId !== userId && message.receiverId !== userId) {
      throw new ForbiddenException('无权限删除此消息');
    }

    await message.update({
      isDeleted: true,
      deletedAt: new Date()
    });
  }

  // 获取未读消息数量
  async getUnreadCount(userId: string, query?: UnreadCountDto): Promise<UnreadCountResponseDto> {
    let whereCondition: any = {
      receiverId: userId,
      isRead: false,
      isDeleted: false
    };

    if (query?.fromUserId) {
      whereCondition.senderId = query.fromUserId;
    }

    const total = await this.messageModel.count({ where: whereCondition });

    // 如果需要按用户分组统计
    let byUser;
    if (!query?.fromUserId) {
      const userCounts = await this.messageModel.findAll({
        attributes: [
          'senderId',
          [fn('COUNT', col('id')), 'count']
        ],
        where: {
          receiverId: userId,
          isRead: false,
          isDeleted: false
        },
        include: [
          {
            model: User,
            as: 'sender',
            attributes: ['name', 'nickName']
          }
        ],
        group: ['senderId'],
        raw: false
      });

      byUser = userCounts.map((item: any) => ({
        userId: item.senderId,
        userName: item.sender?.nickName || item.sender?.name || '未知用户',
        count: parseInt(item.getDataValue('count'))
      }));
    }

    return { total, byUser };
  }

  // 获取消息详情（包含关联信息）
  private async getMessageWithDetails(messageId: string): Promise<MessageResponseDto> {
    const message = await this.messageModel.findByPk(messageId, {
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'name', 'nickName', 'avatar']
        },
        {
          model: User,
          as: 'receiver',
          attributes: ['id', 'name', 'nickName', 'avatar']
        },
        {
          model: UserMessage,
          as: 'replyTo',
          attributes: ['id', 'content', 'senderId'],
          include: [
            {
              model: User,
              as: 'sender',
              attributes: ['name', 'nickName']
            }
          ],
          required: false
        }
      ]
    });

    if (!message) {
      throw new NotFoundException('消息不存在');
    }

    return this.formatMessageResponse(message);
  }

  // 格式化消息响应
  private formatMessageResponse(message: any): MessageResponseDto {
    const response: MessageResponseDto = {
      id: message.id,
      senderId: message.senderId,
      receiverId: message.receiverId,
      content: message.content,
      messageType: message.messageType,
      attachments: message.attachments,
      replyToId: message.replyToId,
      isRead: message.isRead,
      readAt: message.readAt,
      createdAt: message.createdAt,
      updatedAt: message.updatedAt
    };

    if (message.sender) {
      response.sender = {
        id: message.sender.id,
        name: message.sender.name,
        nickName: message.sender.nickName,
        avatar: message.sender.avatar
      };
    }

    if (message.receiver) {
      response.receiver = {
        id: message.receiver.id,
        name: message.receiver.name,
        nickName: message.receiver.nickName,
        avatar: message.receiver.avatar
      };
    }

    if (message.replyTo) {
      response.replyTo = {
        id: message.replyTo.id,
        content: message.replyTo.content,
        senderId: message.replyTo.senderId,
        senderName: message.replyTo.sender?.nickName || message.replyTo.sender?.name || '未知用户'
      };
    }

    return response;
  }
}
