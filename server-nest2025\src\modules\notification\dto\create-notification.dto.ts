import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsDateString, IsBoolean, IsObject } from 'class-validator';

export class CreateNotificationDto {
  @ApiProperty({ description: '通知标题' })
  @IsString()
  title: string;

  @ApiProperty({ description: '通知内容' })
  @IsString()
  content: string;

  @ApiProperty({
    description: '通知类型',
    enum: ['system', 'announcement'],
    example: 'system'
  })
  @IsEnum(['system', 'announcement'])
  type: 'system' | 'announcement';

  @ApiPropertyOptional({ 
    description: '通知优先级', 
    enum: ['info', 'warning', 'success', 'error'],
    default: 'info'
  })
  @IsOptional()
  @IsEnum(['info', 'warning', 'success', 'error'])
  priority?: 'info' | 'warning' | 'success' | 'error';

  @ApiPropertyOptional({ 
    description: '操作数据',
    example: {
      type: 'url',
      value: 'https://example.com',
      buttonText: '查看详情'
    }
  })
  @IsOptional()
  @IsObject()
  actionData?: {
    type?: 'url' | 'route' | 'modal';
    value?: string;
    buttonText?: string;
  };

  @ApiPropertyOptional({ description: '发布时间' })
  @IsOptional()
  @IsDateString()
  publishAt?: Date;

  @ApiPropertyOptional({ description: '过期时间' })
  @IsOptional()
  @IsDateString()
  expireAt?: Date;

  @ApiPropertyOptional({ description: '是否激活', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
