import { createParamDecorator, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import User from '../../db/model/User';
import Account from '../../db/model/Account';
import { AccountType, UserLevel } from 'src/utils/enums';
import { Op, Sequelize } from 'sequelize';

/**
 * 账户用户装饰器，支持与参数accountTypes不符时，抛出异常
 * 用于获取带有账户权限的用户信息
 * 
 * @param accountTypes - 允许的账户类型数组
 * @returns ITokenUser - 包含用户ID、openid、等级、账户类型等信息
 * 
 * 主要功能:
 * 1. 验证用户登录状态
 * 2. 查询用户完整信息(包含账户关联)
 * 3. 验证用户账户权限
 */
export const RequestAccountUser = createParamDecorator(
  async (accountTypes:AccountType[]|undefined, ctx: ExecutionContext):Promise<ITokenUser> => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user as IRequestUser;
    
    if (!user.id) {
      throw new UnauthorizedException('请先登录');
    }

    try {
      // 获取 User 和 Account 模型
      
      // 查询用户信息，包含 account 表关联
      const fullUser = await User.findOne({
        where: { id: user.id },
        attributes: ['id', 'level'],
        include: [
          {
            model: Account,
            attributes: ['type'],
            required: false,
            on: {
              '$User.openid$': { [Op.eq]: Sequelize.col('Account.openid') }
            },
            where: {
              // 你的条件
            }
          }
        ]
        }) as User&{account?:Account};


      if (!fullUser) {
        throw new UnauthorizedException('用户不存在');
      }
      let roleUser:ITokenUser = {
        id: fullUser.id,
        openid: fullUser.openid,
        level: fullUser.level,
        type: fullUser.account?.type || AccountType.Admin,
        ip: request.ip,
      }

      if(accountTypes && accountTypes.length > 0){
        if(!fullUser.account || !accountTypes.includes(fullUser.account.type)){
          throw new UnauthorizedException('用户没有权限');
        }
      }

      return roleUser;
    } catch (error) {
      throw new UnauthorizedException('获取用户信息失败');
    }
  },
);