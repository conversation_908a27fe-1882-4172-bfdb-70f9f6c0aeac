import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { UserMessageController } from './userMessage.controller';
import { UserMessageService } from './userMessage.service';
import UserMessage from '../../db/model/UserMessage';
import User from '../../db/model/User';

@Module({
  imports: [
    SequelizeModule.forFeature([UserMessage, User])
  ],
  controllers: [UserMessageController],
  providers: [UserMessageService],
  exports: [UserMessageService]
})
export class UserMessageModule {}
