import {
  Column,
  DataType,
  Default,
  Table,
  BelongsTo,
  Index,
} from 'sequelize-typescript';
import _BaseUUIDEasyEntity from './_BaseUUIDEasyEntity';
import User from './User';

@Table
export default class UserMessage extends _BaseUUIDEasyEntity {
  @Column({
    type: DataType.UUID,
    defaultValue: DataType.UUIDV4,
    primaryKey: true
  })
  declare id: string;

  @Column({
    type: DataType.UUID,
    allowNull: false,
    comment: '发送者ID'
  })
  declare senderId: string;

  @Column({
    type: DataType.UUID,
    allowNull: false,
    comment: '接收者ID'
  })
  declare receiverId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    comment: '消息内容'
  })
  declare content: string;

  @Column({
    type: DataType.ENUM('text', 'image', 'file', 'system'),
    allowNull: false,
    defaultValue: 'text',
    comment: '消息类型'
  })
  declare messageType: 'text' | 'image' | 'file' | 'system';

  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment: '附件信息'
  })
  declare attachments?: {
    type: string;
    url: string;
    name?: string;
    size?: number;
  }[];

  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: '回复的消息ID'
  })
  declare replyToId?: string;

  @Default(false)
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    comment: '是否已读'
  })
  declare isRead: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '阅读时间'
  })
  declare readAt?: Date;

  @Default(false)
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    comment: '是否删除'
  })
  declare isDeleted: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '删除时间'
  })
  declare deletedAt?: Date;

  // 关联关系
  @BelongsTo(() => User, { foreignKey: 'senderId' })
  declare sender: User;

  @BelongsTo(() => User, { foreignKey: 'receiverId' })
  declare receiver: User;

  @BelongsTo(() => UserMessage, { foreignKey: 'replyToId' })
  declare replyTo?: UserMessage;
}

// 添加索引
Index(['senderId', 'receiverId']);
Index(['receiverId', 'isRead']);
Index(['senderId', 'createdAt']);
Index(['receiverId', 'createdAt']);
