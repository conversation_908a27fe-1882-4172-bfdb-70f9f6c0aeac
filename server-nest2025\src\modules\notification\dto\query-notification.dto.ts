import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString, IsNumber, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class QueryNotificationDto {
  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiPropertyOptional({ 
    description: '通知类型', 
    enum: ['broadcast', 'targeted']
  })
  @IsOptional()
  @IsEnum(['broadcast', 'targeted'])
  type?: 'broadcast' | 'targeted';

  @ApiPropertyOptional({ 
    description: '优先级', 
    enum: ['info', 'warning', 'success', 'error']
  })
  @IsOptional()
  @IsEnum(['info', 'warning', 'success', 'error'])
  priority?: 'info' | 'warning' | 'success' | 'error';

  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  keyword?: string;
}

export class AdminQueryNotificationDto extends QueryNotificationDto {
  @ApiPropertyOptional({ description: '是否激活' })
  @IsOptional()
  @Type(() => Boolean)
  isActive?: boolean;

  @ApiPropertyOptional({ description: '创建者ID' })
  @IsOptional()
  @IsString()
  createdBy?: string;
}
