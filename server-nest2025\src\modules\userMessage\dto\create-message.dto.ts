import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsUUID, IsArray, IsObject } from 'class-validator';

export class CreateMessageDto {
  @ApiProperty({ description: '接收者ID' })
  @IsUUID()
  receiverId: string;

  @ApiProperty({ description: '消息内容' })
  @IsString()
  content: string;

  @ApiPropertyOptional({ 
    description: '消息类型', 
    enum: ['text', 'image', 'file', 'system'],
    default: 'text'
  })
  @IsOptional()
  @IsEnum(['text', 'image', 'file', 'system'])
  messageType?: 'text' | 'image' | 'file' | 'system';

  @ApiPropertyOptional({ 
    description: '附件信息',
    example: [
      {
        type: 'image',
        url: 'https://example.com/image.jpg',
        name: 'image.jpg',
        size: 1024
      }
    ]
  })
  @IsOptional()
  @IsArray()
  attachments?: {
    type: string;
    url: string;
    name?: string;
    size?: number;
  }[];

  @ApiPropertyOptional({ description: '回复的消息ID' })
  @IsOptional()
  @IsUUID()
  replyToId?: string;
}
