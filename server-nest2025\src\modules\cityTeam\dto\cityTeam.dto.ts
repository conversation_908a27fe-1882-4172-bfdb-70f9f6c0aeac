import { IsString, IsOptional, <PERSON><PERSON><PERSON>ber, IsUUID, IsEnum, isString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CityTeamType } from '../../../utils/enums';

export class CityTeamDto {

  @ApiProperty({ 
    description: '城市队ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  @IsOptional()
  id?:number

  @ApiProperty({ 
    description: '城市队名称',
    example: '北京毽球队',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  name: string;

  @ApiProperty({ 
    description: '城市队简介',
    example: '北京地区专业的毽球队伍，欢迎加入',
    minLength: 1,
    maxLength: 500
  })
  @IsString()
  intro: string;

  @ApiProperty({ 
    description: '头像URL',
    example: 'https://example.com/avatar.jpg'
  })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiProperty({ 
    description: '详细内容',
    example: '我们是一支专业的毽球队伍，定期组织训练和比赛...',
    maxLength: 2000
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({ 
    description: '备注信息',
    required: false,
    example: '特殊说明'
  })
  @IsOptional()
  @IsString()
  bak?: string;

  @ApiProperty({ 
    description: '类型：CityTeamType 二进制位',
    example: 0,
    enum: [CityTeamType.小花, CityTeamType.大白, CityTeamType.网毽, CityTeamType.比赛]
  })
  @IsNumber({}, { message: '球队类型必填，可多选' })
  type: number;

  @ApiProperty({ 
    description: '球队人数',
    example: 10
  })
  @IsNumber({}, { message: '球队人数必填' })
  size: number;

  @ApiProperty({ 
    description: '省份',
    required: false,
    example: '北京市'
  })
  @IsOptional()
  @IsString()
  province?: string;

  @ApiProperty({ 
    description: '城市',
    required: false,
    example: '北京市'
  })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiProperty({ 
    description: '国家',
    required: false,
    example: '中国'
  })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiProperty({ 
    description: '活动地址',
    required: false,
    example: '朝阳区某某街道123号'
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({ 
    description: '行政编码',
    required: false,
    example: 110000
  })
  @IsNumber({}, { message: '城市行政编码必填' })
  divisionCode: number;

  @ApiProperty({ 
    description: '队长姓名',
    example: '张三'
  })
  @IsString({message: "队长必填"})
  leader: string;

  @ApiProperty({ 
    description: '队长联系方式',
    example: '13800138000'
  })
  @IsString({message: "队长联系方式必填"})
  leaderLink: string;

  @ApiProperty({ 
    description: '图片列表，JSON字符串格式',
    example: '["https://example.com/img1.jpg", "https://example.com/img2.jpg"]'
  })
  @IsString()
  @IsOptional()
  images?: string;

  @ApiProperty({ 
    description: '运动类型ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  @IsOptional()
  SportId?: string;
} 