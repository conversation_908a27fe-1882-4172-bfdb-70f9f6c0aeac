import {
  Column,
  DataType,
  Default,
  Table,
  BelongsTo,
} from 'sequelize-typescript';
import _BaseUUIDEasyEntity from './_BaseUUIDEasyEntity';
import User from './User';

@Table
export default class SystemNotification extends _BaseUUIDEasyEntity {


  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '通知标题'
  })
  declare title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    comment: '通知内容'
  })
  declare content: string;

  @Column({
    type: DataType.ENUM('broadcast', 'targeted'),
    allowNull: false,
    comment: '通知类型：broadcast-广播，targeted-定向'
  })
  declare type: 'broadcast' | 'targeted';

  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment: '目标用户条件'
  })
  declare targetConditions?: {
    provinces?: string[];
    cities?: string[];
    minLevel?: number;
    maxLevel?: number;
    userTypes?: string[];
    divisionCodes?: string[];
  };

  @Column({
    type: DataType.ENUM('info', 'warning', 'success', 'error'),
    allowNull: false,
    defaultValue: 'info',
    comment: '通知优先级'
  })
  declare priority: 'info' | 'warning' | 'success' | 'error';

  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment: '操作数据'
  })
  declare actionData?: {
    type?: 'url' | 'route' | 'modal';
    value?: string;
    buttonText?: string;
  };

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    comment: '发布时间'
  })
  declare publishAt: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '过期时间'
  })
  declare expireAt?: Date;

  @Default(true)
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    comment: '是否激活'
  })
  declare isActive: boolean;

  @Column({
    type: DataType.UUID,
    allowNull: false,
    comment: '创建者ID'
  })
  declare createdBy: string;

  // 关联关系
  @BelongsTo(() => User, { foreignKey: 'createdBy' })
  declare creator: User;
}
