import { Column, Model, DataType, Table, BelongsTo, ForeignKey } from 'sequelize-typescript';
import BaseUUIDEntity from './_BaseUUIDEntity';
import League from './League';
import Team from './Team';

@Table({
  timestamps: true
})
export default class LeagueTeamResult extends BaseUUIDEntity {

  @Column({
    type: DataType.DECIMAL,
    defaultValue: 0,
    comment: '总分'
  })
  declare score: number;

  @Column({
    type: DataType.STRING(1000)
  })
  declare content: string;

  // 关联字段
  @ForeignKey(() => League)
  @Column(DataType.UUID)
  declare LeagueId: string;

  @ForeignKey(() => Team)
  @Column(DataType.UUID)
  declare TeamId: string;

  @BelongsTo(() => League)
  declare League: League;

  @BelongsTo(() => Team, { foreignKey: 'TeamId' })
  declare Team: Team
}