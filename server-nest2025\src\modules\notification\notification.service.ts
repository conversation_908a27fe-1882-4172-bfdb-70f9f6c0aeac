import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import SystemNotification from '../../db/model/SystemNotification';
import User from '../../db/model/User';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { QueryNotificationDto, AdminQueryNotificationDto } from './dto/query-notification.dto';
import { NotificationListResponseDto, NotificationStatsDto } from './dto/notification-response.dto';

@Injectable()
export class NotificationService {
  constructor(
    @InjectModel(SystemNotification)
    private readonly notificationModel: typeof SystemNotification,
    @InjectModel(User)
    private readonly userModel: typeof User,
  ) {}

  // 获取用户可见的通知列表
  async getUserNotifications(userId: string, query: QueryNotificationDto): Promise<NotificationListResponseDto> {
    const user = await this.userModel.findByPk(userId);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    const { page = 1, limit = 10, type, priority, keyword } = query;
    const offset = (page - 1) * limit;

    // 构建查询条件
    const whereConditions: any = {
      isActive: true,
      publishAt: { [Op.lte]: new Date() },
      [Op.or]: [
        { expireAt: null },
        { expireAt: { [Op.gt]: new Date() } }
      ]
    };

    if (type) {
      whereConditions.type = type;
    }

    if (priority) {
      whereConditions.priority = priority;
    }

    if (keyword) {
      whereConditions[Op.or] = [
        { title: { [Op.like]: `%${keyword}%` } },
        { content: { [Op.like]: `%${keyword}%` } }
      ];
    }

    // 获取所有符合条件的通知
    const { rows: allNotifications, count: totalCount } = await this.notificationModel.findAndCountAll({
      where: whereConditions,
      order: [['publishAt', 'DESC']],
      limit,
      offset
    });

    // 过滤用户可见的通知
    const visibleNotifications = allNotifications.filter(notification => 
      this.isNotificationVisibleToUser(notification, user)
    );

    const totalPages = Math.ceil(totalCount / limit);

    return {
      list: visibleNotifications.map(n => n.toJSON()),
      total: totalCount,
      page,
      limit,
      totalPages
    };
  }

  // 管理员获取通知列表
  async getAdminNotifications(query: AdminQueryNotificationDto): Promise<NotificationListResponseDto> {
    const { page = 1, limit = 10, type, priority, keyword, isActive, createdBy } = query;
    const offset = (page - 1) * limit;

    const whereConditions: any = {};

    if (type) {
      whereConditions.type = type;
    }

    if (priority) {
      whereConditions.priority = priority;
    }

    if (typeof isActive === 'boolean') {
      whereConditions.isActive = isActive;
    }

    if (createdBy) {
      whereConditions.createdBy = createdBy;
    }

    if (keyword) {
      whereConditions[Op.or] = [
        { title: { [Op.like]: `%${keyword}%` } },
        { content: { [Op.like]: `%${keyword}%` } }
      ];
    }

    const { rows: notifications, count: total } = await this.notificationModel.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'name', 'nickName']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(total / limit);

    return {
      list: notifications.map(n => n.toJSON()),
      total,
      page,
      limit,
      totalPages
    };
  }

  // 创建通知
  async createNotification(createDto: CreateNotificationDto, createdBy: string): Promise<SystemNotification> {
    const notificationData = {
      ...createDto,
      createdBy,
      publishAt: createDto.publishAt || new Date(),
      priority: createDto.priority || 'info',
      isActive: createDto.isActive !== false
    };

    return await this.notificationModel.create(notificationData);
  }

  // 更新通知
  async updateNotification(id: string, updateDto: UpdateNotificationDto): Promise<SystemNotification> {
    const notification = await this.notificationModel.findByPk(id);
    if (!notification) {
      throw new NotFoundException('通知不存在');
    }

    await notification.update(updateDto);
    return notification;
  }

  // 删除通知
  async deleteNotification(id: string): Promise<void> {
    const notification = await this.notificationModel.findByPk(id);
    if (!notification) {
      throw new NotFoundException('通知不存在');
    }

    await notification.destroy();
  }

  // 获取通知详情
  async getNotificationById(id: string): Promise<SystemNotification> {
    const notification = await this.notificationModel.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'name', 'nickName']
        }
      ]
    });

    if (!notification) {
      throw new NotFoundException('通知不存在');
    }

    return notification;
  }

  // 获取通知统计
  async getNotificationStats(): Promise<NotificationStatsDto> {
    const total = await this.notificationModel.count();
    const active = await this.notificationModel.count({ where: { isActive: true } });
    const expired = await this.notificationModel.count({
      where: {
        expireAt: { [Op.lt]: new Date() }
      }
    });
    const broadcast = await this.notificationModel.count({ where: { type: 'broadcast' } });
    const targeted = await this.notificationModel.count({ where: { type: 'targeted' } });

    return { total, active, expired, broadcast, targeted };
  }

  // 检查通知对用户是否可见
  private isNotificationVisibleToUser(notification: SystemNotification, user: User): boolean {
    if (notification.type === 'broadcast') return true;

    const conditions = notification.targetConditions;
    if (!conditions) return true;

    // 检查省份
    if (conditions.provinces?.length && !conditions.provinces.includes(user.province)) {
      return false;
    }

    // 检查城市
    if (conditions.cities?.length && !conditions.cities.includes(user.city)) {
      return false;
    }

    // 检查等级
    if (conditions.minLevel && user.level < conditions.minLevel) {
      return false;
    }

    if (conditions.maxLevel && user.level > conditions.maxLevel) {
      return false;
    }

    // 检查行政区划代码
    if (conditions.divisionCodes?.length && !conditions.divisionCodes.includes(user.divisionCode?.toString())) {
      return false;
    }

    return true;
  }
}
