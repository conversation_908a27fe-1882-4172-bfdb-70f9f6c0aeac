import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class NotificationResponseDto {
  @ApiProperty({ description: '通知ID' })
  id: string;

  @ApiProperty({ description: '通知标题' })
  title: string;

  @ApiProperty({ description: '通知内容' })
  content: string;

  @ApiProperty({
    description: '通知类型',
    enum: ['system', 'announcement']
  })
  type: 'system' | 'announcement';

  @ApiProperty({ 
    description: '通知优先级', 
    enum: ['info', 'warning', 'success', 'error']
  })
  priority: 'info' | 'warning' | 'success' | 'error';

  @ApiPropertyOptional({ description: '操作数据' })
  actionData?: {
    type?: 'url' | 'route' | 'modal';
    value?: string;
    buttonText?: string;
  };

  @ApiProperty({ description: '发布时间' })
  publishAt: Date;

  @ApiPropertyOptional({ description: '过期时间' })
  expireAt?: Date;

  @ApiProperty({ description: '是否激活' })
  isActive: boolean;

  @ApiProperty({ description: '创建者ID' })
  createdBy: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class NotificationListResponseDto {
  @ApiProperty({ description: '通知列表', type: [NotificationResponseDto] })
  list: NotificationResponseDto[];

  @ApiProperty({ description: '总数' })
  total: number;

  @ApiProperty({ description: '当前页' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;

  @ApiProperty({ description: '总页数' })
  totalPages: number;
}

export class NotificationStatsDto {
  @ApiProperty({ description: '总通知数' })
  total: number;

  @ApiProperty({ description: '激活通知数' })
  active: number;

  @ApiProperty({ description: '过期通知数' })
  expired: number;

  @ApiProperty({ description: '系统通知数' })
  system: number;

  @ApiProperty({ description: '公告数' })
  announcement: number;
}
