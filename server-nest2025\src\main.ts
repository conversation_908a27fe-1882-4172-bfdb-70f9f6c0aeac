console.log('当前运行环境:', __dirname, process.env.NODE_ENV);
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import siteConfig from './siteConfig';
import { DateFormatInterceptor } from './utils/date-format.interceptor';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // 配置全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // 只保留 DTO 定义的字段
      forbidNonWhitelisted: false, // 如果有多余字段，直接抛出异常（可选）
      transform: true, // 自动类型转换
    }),
  );

  // app.useGlobalInterceptors(new DateFormatInterceptor());

  // 配置Swagger文档
  const config = new DocumentBuilder()
    .setTitle('毽球比赛系统 API')
    .setDescription('毽球比赛系统的后端API文档')
    .setVersion('1.0')
    .addTag('城市队', '城市队相关接口')
    .addTag('用户', '用户相关接口')
    .addTag('场地', '场地相关接口')
    .addTag('比赛', '比赛相关接口')
    .addTag('联赛', '联赛相关接口')
    .addTag('队伍', '队伍相关接口')
    .addTag('聚会', '聚会相关接口')
    .addTag('评论', '评论相关接口')
    .addTag('文档', '文档相关接口')
    .addTag('我的', '个人中心相关接口')
    .addTag('微信', '微信相关接口')
    .addTag('缓存', '缓存相关接口')
    .addTag('通用', '通用接口')
    .addTag('系统通知', '系统通知相关接口')
    .addTag('系统通知管理', '系统通知管理接口')
    .addTag('用户消息', '用户消息相关接口')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: '输入JWT token',
        in: 'header',
      },
      'JWT-auth', // This name here is important for references
    )
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
    customSiteTitle: '毽球比赛系统 API 文档',
  });

  await app.listen(siteConfig.PORT ?? 3000);
  console.log(`🚀 应用已启动，端口: ${siteConfig.PORT ?? 3000}`);
  console.log(`📚 Swagger文档地址: http://localhost:${siteConfig.PORT ?? 3000}/api`);
}
bootstrap();
