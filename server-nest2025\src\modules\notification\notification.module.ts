import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { NotificationController, AdminNotificationController } from './notification.controller';
import { NotificationService } from './notification.service';
import SystemNotification from '../../db/model/SystemNotification';
import User from '../../db/model/User';

@Module({
  imports: [
    SequelizeModule.forFeature([SystemNotification, User])
  ],
  controllers: [NotificationController, AdminNotificationController],
  providers: [NotificationService],
  exports: [NotificationService]
})
export class NotificationModule {}
