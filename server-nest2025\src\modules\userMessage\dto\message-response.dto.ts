import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class MessageResponseDto {
  @ApiProperty({ description: '消息ID' })
  id: string;

  @ApiProperty({ description: '发送者ID' })
  senderId: string;

  @ApiProperty({ description: '接收者ID' })
  receiverId: string;

  @ApiProperty({ description: '消息内容' })
  content: string;

  @ApiProperty({ 
    description: '消息类型', 
    enum: ['text', 'image', 'file', 'system']
  })
  messageType: 'text' | 'image' | 'file' | 'system';

  @ApiPropertyOptional({ description: '附件信息' })
  attachments?: {
    type: string;
    url: string;
    name?: string;
    size?: number;
  }[];

  @ApiPropertyOptional({ description: '回复的消息ID' })
  replyToId?: string;

  @ApiProperty({ description: '是否已读' })
  isRead: boolean;

  @ApiPropertyOptional({ description: '阅读时间' })
  readAt?: Date;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 关联数据
  @ApiPropertyOptional({ description: '发送者信息' })
  sender?: {
    id: string;
    name: string;
    nickName: string;
    avatar: string;
  };

  @ApiPropertyOptional({ description: '接收者信息' })
  receiver?: {
    id: string;
    name: string;
    nickName: string;
    avatar: string;
  };

  @ApiPropertyOptional({ description: '回复的消息' })
  replyTo?: {
    id: string;
    content: string;
    senderId: string;
    senderName: string;
  };
}

export class MessageListResponseDto {
  @ApiProperty({ description: '消息列表', type: [MessageResponseDto] })
  list: MessageResponseDto[];

  @ApiProperty({ description: '总数' })
  total: number;

  @ApiProperty({ description: '当前页' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;

  @ApiProperty({ description: '总页数' })
  totalPages: number;
}

export class ConversationDto {
  @ApiProperty({ description: '对话用户ID' })
  userId: string;

  @ApiProperty({ description: '对话用户信息' })
  userInfo: {
    id: string;
    name: string;
    nickName: string;
    avatar: string;
  };

  @ApiProperty({ description: '最后一条消息' })
  lastMessage: {
    id: string;
    content: string;
    messageType: string;
    createdAt: Date;
    isRead: boolean;
    senderId: string;
  };

  @ApiProperty({ description: '未读消息数' })
  unreadCount: number;

  @ApiProperty({ description: '最后更新时间' })
  lastUpdateTime: Date;
}

export class ConversationListDto {
  @ApiProperty({ description: '对话列表', type: [ConversationDto] })
  list: ConversationDto[];

  @ApiProperty({ description: '总数' })
  total: number;
}

export class UnreadCountResponseDto {
  @ApiProperty({ description: '未读消息总数' })
  total: number;

  @ApiPropertyOptional({ description: '按用户分组的未读数' })
  byUser?: {
    userId: string;
    userName: string;
    count: number;
  }[];
}
