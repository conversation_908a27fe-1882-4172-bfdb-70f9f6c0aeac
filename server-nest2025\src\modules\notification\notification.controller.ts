import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards 
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { NotificationService } from './notification.service';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { QueryNotificationDto, AdminQueryNotificationDto } from './dto/query-notification.dto';
import { 
  NotificationResponseDto, 
  NotificationListResponseDto, 
  NotificationStatsDto 
} from './dto/notification-response.dto';
import { RequestUser } from '../../decorators/request-user.decorator';
import config from '../../siteConfig';

@ApiTags('系统通知')
@Controller(`${config.API_PREFIX}/notification`)
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get('list')
  @ApiOperation({ summary: '获取用户通知列表' })
  @ApiResponse({ status: 200, description: '成功获取通知列表', type: NotificationListResponseDto })
  @ApiBearerAuth('JWT-auth')
  async getUserNotifications(
    @Query() query: QueryNotificationDto,
    @RequestUser() user: any
  ): Promise<NotificationListResponseDto> {
    return await this.notificationService.getUserNotifications(user.id, query);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取通知详情' })
  @ApiResponse({ status: 200, description: '成功获取通知详情', type: NotificationResponseDto })
  @ApiBearerAuth('JWT-auth')
  async getNotificationById(@Param('id') id: string): Promise<NotificationResponseDto> {
    const notification = await this.notificationService.getNotificationById(id);
    return notification.toJSON();
  }
}

@ApiTags('系统通知管理')
@Controller(`${config.API_PREFIX}/admin/notification`)
export class AdminNotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get('list')
  @ApiOperation({ summary: '管理员获取通知列表' })
  @ApiResponse({ status: 200, description: '成功获取通知列表', type: NotificationListResponseDto })
  @ApiBearerAuth('JWT-auth')
  async getAdminNotifications(
    @Query() query: AdminQueryNotificationDto
  ): Promise<NotificationListResponseDto> {
    return await this.notificationService.getAdminNotifications(query);
  }

  @Post()
  @ApiOperation({ summary: '创建系统通知' })
  @ApiResponse({ status: 201, description: '成功创建通知', type: NotificationResponseDto })
  @ApiBearerAuth('JWT-auth')
  async createNotification(
    @Body() createDto: CreateNotificationDto,
    @RequestUser() user: any
  ): Promise<NotificationResponseDto> {
    const notification = await this.notificationService.createNotification(createDto, user.id);
    return notification.toJSON();
  }

  @Put(':id')
  @ApiOperation({ summary: '更新系统通知' })
  @ApiResponse({ status: 200, description: '成功更新通知', type: NotificationResponseDto })
  @ApiBearerAuth('JWT-auth')
  async updateNotification(
    @Param('id') id: string,
    @Body() updateDto: UpdateNotificationDto
  ): Promise<NotificationResponseDto> {
    const notification = await this.notificationService.updateNotification(id, updateDto);
    return notification.toJSON();
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除系统通知' })
  @ApiResponse({ status: 200, description: '成功删除通知' })
  @ApiBearerAuth('JWT-auth')
  async deleteNotification(@Param('id') id: string): Promise<{ message: string }> {
    await this.notificationService.deleteNotification(id);
    return { message: '通知删除成功' };
  }

  @Get('stats')
  @ApiOperation({ summary: '获取通知统计信息' })
  @ApiResponse({ status: 200, description: '成功获取统计信息', type: NotificationStatsDto })
  @ApiBearerAuth('JWT-auth')
  async getNotificationStats(): Promise<NotificationStatsDto> {
    return await this.notificationService.getNotificationStats();
  }

  @Get(':id')
  @ApiOperation({ summary: '管理员获取通知详情' })
  @ApiResponse({ status: 200, description: '成功获取通知详情', type: NotificationResponseDto })
  @ApiBearerAuth('JWT-auth')
  async getNotificationById(@Param('id') id: string): Promise<NotificationResponseDto> {
    const notification = await this.notificationService.getNotificationById(id);
    return notification.toJSON();
  }
}
