import { SetMetadata } from '@nestjs/common';
import { AccountType, UserLevel } from '../../utils/enums';

/**
 * 角色装饰器
 * 用于设置接口的访问权限
 * 
 * 主要功能:
 * 1. 验证用户的账户类型(AccountType)
 * 2. 验证用户等级(UserLevel)
 * 3. 支持多重角色验证
 * 
 * 使用方式:
 * @Roles(AccountType.Admin, UserLevel.Admin)
 */
export const ROLES_KEY = 'roles';
export const ACCOUNT_TYPES_KEY = 'accountTypes';
export const USER_LEVELS_KEY = 'userLevels';


export const Roles = (...roles: (AccountType | UserLevel)[]) => {
  const accountTypes = roles.filter(role => Object.values(AccountType).includes(role as AccountType));
  const userLevels = roles.filter(role => Object.values(UserLevel).includes(role as UserLevel));

  return (target: any, key?: string, descriptor?: any) => {
    if (accountTypes.length > 0) {
      SetMetadata(ACCOUNT_TYPES_KEY, accountTypes)(target, key || '', descriptor);
    }
    if (userLevels.length > 0) {
      SetMetadata(USER_LEVELS_KEY, userLevels)(target, key || '', descriptor);
    }
  };
};